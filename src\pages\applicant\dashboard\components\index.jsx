import React, { useState, useEffect } from 'react';
import { useGetApplicationsQuery, useGetApplicationDetailsQuery } from 'pages/applicant/application/api';
import {
  MyApplication,
  NewApplication,
  VStack,
  useDisclosure
} from 'common/components';
import { PopupModal } from 'common/custom-components';
import { useLocation } from 'react-router-dom';
import ApplicationView from 'pages/applicant/application/components/ApplicationView';
import { t } from 'i18next';

const Dashboard = () => {
  const location = useLocation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedApplication, setSelectedApplication] = useState(null);

  const ShowNewApplication = location.pathname === '/ui/applicant/dashboard';
  const [queryParams, setQueryParams] = useState({
    page: 0,
    size: 5,
    sort: [],
    search: '',
    status: ''
  });

  const {
    data: { payload: applicationResponse = {} } = {},
    isLoading
  } = useGetApplicationsQuery(queryParams, {
    refetchOnMountOrArgChange: true
  });

  // API query for application details (only when selectedApplication is set)
  const {
    data: { payload: applicationDetails = {} } = {},
    isLoading: isDetailsLoading,
    isSuccess: isDetailsSuccess
  } = useGetApplicationDetailsQuery(selectedApplication?.id, {
    skip: !selectedApplication?.id
  });

  const {
    content: applicationData = [],
    totalElements = 0
  } = applicationResponse;

  const transformedApplicationData = Array.isArray(applicationData)
    ? applicationData.map((item, index) => ({
      ...item,
      slNo: String((queryParams.page * queryParams.size) + index + 1).padStart(2, '0')
    }))
    : [];

  const handleSearchChange = (search) => {
    setQueryParams((prev) => ({
      ...prev,
      search,
      page: 0
    }));
  };

  const handleFilterChange = (status) => {
    setQueryParams((prev) => ({
      ...prev,
      status,
      page: 0
    }));
  };

  const handlePageChange = (page) => {
    setQueryParams((prev) => ({
      ...prev,
      page: page - 1
    }));
  };

  const handleViewApplication = (application) => {
    setSelectedApplication(application);
  };

  const handleCloseModal = () => {
    setSelectedApplication(null);
    onClose();
  };

  useEffect(() => {
    if (selectedApplication?.id && isDetailsSuccess) {
      onOpen();
    }
  }, [selectedApplication?.id, isDetailsSuccess, onOpen]);

  return (
    <VStack spacing={8} align="stretch">
      {/* My Applications Section */}
      {(transformedApplicationData.length > 0 || !ShowNewApplication) && (
      <MyApplication
        applicationData={transformedApplicationData}
        loading={isLoading}
        onSearchChange={handleSearchChange}
        onFilterChange={handleFilterChange}
        onPageChange={handlePageChange}
        onViewApplication={handleViewApplication}
        itemsPerPage={queryParams.size}
        totalItems={totalElements}
      />
      )}

      {/* New Applications Section */}
      {ShowNewApplication && (
        <NewApplication />)}

      {/* Application View Modal */}
      <PopupModal
        isOpen={isOpen}
        onClose={handleCloseModal}
        title={t('scholarshipApplication')}
        size="6xl"
        loading={isDetailsLoading}
        showCloseButton
        showDownloadButton
        showPrintButton
        onDownload={() => {
          // Handle download functionality
        }}
        onPrint={() => {
          // Handle print functionality
          window.print();
        }}
        closeOnOverlayClick
      >
        {selectedApplication && (
          <ApplicationView
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
          />
        )}
      </PopupModal>
    </VStack>
  );
};

export default Dashboard;

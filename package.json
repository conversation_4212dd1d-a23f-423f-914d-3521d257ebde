{"name": "norka-root", "private": true, "version": "0.0.42", "type": "module", "husky": {"hooks": {"prepare-commit-msg": "exec < /dev/tty && pnpm cz --hook || true"}}, "scripts": {"eslint": "eslint src --ext .js,.jsx --max-warnings=0", "eslint:fix": "eslint src --ext .js,.jsx --fix", "dev": "vite --port 5003 --strictPort", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "commit": "npx cz"}, "lint-staged": {"**/*.{js,jsx}": ["pnpm run eslint"]}, "dependencies": {"@chakra-ui/react": "^2.8.2", "@emotion/react": "^11", "@emotion/styled": "^11", "@hookform/resolvers": "^3.9.0", "@reduxjs/toolkit": "^2.2.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "framer-motion": "^10", "i18next": "^23.11.2", "lodash-es": "^4.17.21", "pnpm": "^10.12.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.53.0", "react-i18next": "^14.1.0", "react-icons": "^5.5.0", "react-pdf": "^7.5.1", "react-redux": "^9.1.1", "react-router-dom": "^6.22.3", "react-select": "^5.10.1", "redux-logger": "^3.0.6", "reselect": "^5.1.1", "yup": "^1.6.1"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.6", "husky": "^9.1.7", "lint-staged": "^15.4.3", "vite": "^5.2.0", "vite-jsconfig-paths": "^2.0.1", "vitest": "^3.2.4"}}
import { ROUTE_URL } from 'common/routeUrls';
import { lazy } from 'react';

const Applicant = lazy(() => import('./application/components'));
const Dashboard = lazy(() => import('./dashboard/components'));
const NewApplication = lazy(() => import('./new-application/components'));
const ApplicationViewPage = lazy(() => import('./application/components/ApplicationView'));

const routes = [
  {
    path: ROUTE_URL.APPLICANT.BASE.APPLICATION,
    element: <Applicant />
  },
  {
    path: ROUTE_URL.APPLICANT.BASE.APPLICATION_WITH_ID,
    element: <Applicant />
  },
  {
    path: ROUTE_URL.APPLICANT.BASE.APPLICATION_VIEW,
    element: <ApplicationViewPage />
  },
  {
    path: ROUTE_URL.APPLICANT.BASE.DASHBOARD,
    element: <Dashboard />
  },
  {
    path: ROUTE_URL.APPLICANT.BASE.NEW_APPLICATION,
    element: <NewApplication />
  },
  {
    path: ROUTE_URL.APPLICANT.BASE.MY_APPLICATIONS,
    element: <Dashboard />
  }
];

export { routes };

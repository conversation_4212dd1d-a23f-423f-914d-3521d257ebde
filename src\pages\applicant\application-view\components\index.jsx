import React, { useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import {
  Box,
  Flex,
  Text,
  IconButton,
  Spinner,
  useColorModeValue,
  Button,
  HStack
} from 'common/components';
import { useGetApplicationDetailsQuery } from 'pages/applicant/application/api';
import ApplicationView from 'pages/applicant/application/components/ApplicationView';
import { ArrowBackIcon, DownloadIcon, PrintIcon } from 'assets/svg';
import { t } from 'i18next';
import { ROUTE_URL } from 'common/routeUrls';
import { commonActions } from 'store/common';
import { _ } from 'utils/lodash';

const ApplicationViewPage = () => {
  const { applicationId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const headerBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const {
    data: { payload: applicationDetails = {} } = {},
    isLoading: isDetailsLoading,
    isSuccess: isDetailsSuccess,
    isError: isDetailsError
  } = useGetApplicationDetailsQuery(applicationId, {
    skip: !applicationId
  });

  // Redirect to dashboard on error or invalid data
  useEffect(() => {
    const shouldRedirect = () => {
      if (isDetailsError) return true;

      if (isDetailsSuccess) {
        if (_.isEmpty(applicationDetails)) return true;

        const { personalDetails = {} } = applicationDetails;
        if (_.isEmpty(personalDetails)) return true;
      }

      return false;
    };

    if (shouldRedirect()) {
      dispatch(
        commonActions.navigateTo({
          to: `ui/${ROUTE_URL.APPLICANT.BASE.DASHBOARD}`,
          replace: true
        })
      );
    }
  }, [isDetailsSuccess, isDetailsError, applicationDetails, dispatch]);

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleDownload = () => {
    // Handle download functionality
    console.log('Download application');
  };

  const handlePrint = () => {
    // Handle print functionality
    window.print();
  };

  // Show loading state
  if (isDetailsLoading) {
    return (
      <Box minH="100vh" bg={headerBg}>
        <Flex
          direction="column"
          align="center"
          justify="center"
          minH="100vh"
          gap={4}
        >
          <Spinner
            size="xl"
            color="primary.500"
            thickness="4px"
          />
          <Text
            fontSize="md"
            color="gray.600"
            fontWeight="medium"
          >
            {t('loadingApplicationDetails')}
          </Text>
        </Flex>
      </Box>
    );
  }

  // Show error state or redirect if no data
  if (isDetailsError || _.isEmpty(applicationDetails)) {
    return null; // Will redirect via useEffect
  }

  return (
    <Box minH="100vh" bg={headerBg}>
      {/* Header with navigation and actions */}
      <Box
        bg={headerBg}
        borderBottom="1px solid"
        borderColor={borderColor}
        position="sticky"
        top={0}
        zIndex={10}
        px={6}
        py={4}
      >
        <Flex align="center" justify="space-between">
          {/* Left side - Back button and title */}
          <HStack spacing={4}>
            <IconButton
              icon={<ArrowBackIcon boxSize={5} />}
              aria-label="Go back"
              size="md"
              variant="ghost"
              borderRadius="full"
              color="gray.600"
              _hover={{
                bg: 'gray.100',
                color: 'gray.800'
              }}
              onClick={handleGoBack}
            />
            <Text
              fontSize="xl"
              fontWeight="semibold"
              color="primary.500"
            >
              {t('scholarshipApplication')}
            </Text>
          </HStack>

          {/* Right side - Action buttons */}
          <HStack spacing={3}>
            <Button
              leftIcon={<DownloadIcon boxSize={4} />}
              size="sm"
              variant="outline"
              colorScheme="primary"
              borderRadius="full"
              onClick={handleDownload}
            >
              {t('download')}
            </Button>
            <Button
              leftIcon={<PrintIcon boxSize={4} />}
              size="sm"
              variant="outline"
              colorScheme="primary"
              borderRadius="full"
              onClick={handlePrint}
            >
              {t('print')}
            </Button>
          </HStack>
        </Flex>
      </Box>

      {/* Application View Content */}
      <ApplicationView
        applicationDetails={applicationDetails}
        isDetailsSuccess={isDetailsSuccess}
      />
    </Box>
  );
};

export default ApplicationViewPage;

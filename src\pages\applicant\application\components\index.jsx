import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { Box, CircularProgress } from 'common/components';
import { StepperStep } from 'common/custom-components';
import { actions as commonActions } from 'pages/common/slice';
import { SCHOLARSHIP_TYPE } from 'pages/common/constant';
import { ROUTE_URL } from 'common';
import { _ } from 'utils/lodash';
import BankDetails from './BankDetails';
import AcademicDetails from './AcademicDetails';
import Documents from './Documents';
import ApplicantDetailsMain from './ApplicantDetailsMain';
import ReviewSubmit from './ReviewSubmit';
import ParentDetailsMain from './ParentDetailsMain';
import { STEPPER_STEPS } from '../constants';
import { actions as sliceActions } from '../slice';
import { getCurrentStep, getApplicationId } from '../selectors';
import { useGetApplicationDetailsQuery } from '../api';

const Applicant = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { applicationId: urlApplicationId } = useParams();
  const currentStep = useSelector(getCurrentStep);
  const storeApplicationId = useSelector(getApplicationId);

  const applicationId = urlApplicationId || storeApplicationId;
  const {
    data: { payload: applicationDetails = {} } = {},
    isSuccess: isDetailsSuccess,
    isLoading: isDetailsLoading,
    isError: isDetailsError
  } = useGetApplicationDetailsQuery(applicationId, {
    skip: !applicationId
  });

  // Redirect to dashboard on error or invalid data
  useEffect(() => {
    const shouldRedirect = () => {
      if (isDetailsError) return true;

      if (isDetailsSuccess) {
        if (_.isEmpty(applicationDetails)) return true;

        const { personalDetails = {} } = applicationDetails;
        const { educationQualification } = personalDetails;

        if (!educationQualification?.scholarshipType) return true;
      }

      return false;
    };

    if (shouldRedirect()) {
      dispatch(
        commonActions.navigateTo({
          to: `ui/${ROUTE_URL.APPLICANT.BASE.DASHBOARD}`,
          replace: true
        })
      );
    }
  }, [isDetailsSuccess, isDetailsError, applicationDetails, dispatch]);

  useEffect(() => {
    if (urlApplicationId && !storeApplicationId) {
      dispatch(sliceActions.setApplicationId(urlApplicationId));
    } else if (!urlApplicationId && storeApplicationId) {
      dispatch(sliceActions.clearAll());
    }
  }, [urlApplicationId, storeApplicationId, dispatch]);

  useEffect(() => {
    if (!isDetailsSuccess || !applicationDetails) return;

    const {
      personalDetails = {},
      parentGuardianDetails = {}
    } = applicationDetails;

    const { educationQualification } = personalDetails;

    if (educationQualification?.id) {
      dispatch(commonActions.setScholarshipTypeId(educationQualification.id));
      localStorage.setItem('scholarshipTypeId', educationQualification.id);
    }

    const scholarshipType = SCHOLARSHIP_TYPE[educationQualification?.scholarshipType];
    if (scholarshipType) {
      dispatch(commonActions.setScholarshipType(scholarshipType));
      localStorage.setItem('scholarshipType', scholarshipType);
    }

    if (parentGuardianDetails?.id) {
      dispatch(sliceActions.setFormId({
        formType: 'parentGuardianId',
        id: parentGuardianDetails.id
      }));
    }
  }, [isDetailsSuccess, applicationDetails, dispatch]);

  const steps = [
    t('personalDetails'),
    t('parentGuardianInfo'),
    t('bankDetails'),
    t('academicDetails'),
    t('documents'),
    t('reviewSubmit')
  ];

  const handleNext = () => {
    dispatch(sliceActions.nextStep());
  };

  const handlePrevious = () => {
    dispatch(sliceActions.previousStep());
  };

  const handleEdit = (section) => {
    const stepMap = {
      [STEPPER_STEPS.APPLICANT_DETAILS]: 0,
      [STEPPER_STEPS.PARENT_DETAILS]: 1,
      [STEPPER_STEPS.BANK_DETAILS]: 2,
      [STEPPER_STEPS.ACADEMIC_DETAILS]: 3,
      [STEPPER_STEPS.DOCUMENTS_UPLOAD]: 4,
      [STEPPER_STEPS.REVIEW_SUBMIT]: 5
    };
    dispatch(sliceActions.setCurrentStep(stepMap[section]));
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <ApplicantDetailsMain
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
            applicationId={applicationId}
          />
        );
      case 1:
        return (
          <ParentDetailsMain
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
            applicationId={applicationId}
          />
        );
      case 2:
        return (
          <BankDetails
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
          />
        );
      case 3:
        return (
          <AcademicDetails
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
          />
        );
      case 4:
        return (
          <Documents
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
          />
        );
      case 5:
        return (
          <ReviewSubmit
            onPrevious={currentStep > 0 ? handlePrevious : null}
            onEdit={handleEdit}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
            applicationId={applicationId}
          />
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    const storedId = localStorage.getItem('scholarshipTypeId');
    const storedName = localStorage.getItem('scholarshipType');

    if (storedId) {
      dispatch(commonActions.setScholarshipTypeId(storedId));
    }
    if (storedName) {
      dispatch(commonActions.setScholarshipType(storedName));
    }
  }, [dispatch]);

  // Show loading state while data is being fetched
  if (isDetailsLoading) {
    return (
      <Box
        py={{ base: 2, md: 4 }}
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
      >
        <CircularProgress
          isIndeterminate
          size="40px"
          thickness="8px"
          trackColor="secondary.500"
          color="primary.500"
        />
      </Box>
    );
  }

  return (
    <Box py={{ base: 2, md: 4 }}>
      {/* Stepper */}
      <Box mb={{ base: 0, md: 8 }} display={{ base: 'none', md: 'block' }}>
        <StepperStep
          steps={steps}
          currentStep={currentStep}
          spacing={{ base: 2, md: 4 }}
        />
      </Box>

      {/* Current Step Content */}
      {renderCurrentStep()}
    </Box>
  );
};

export default Applicant;
